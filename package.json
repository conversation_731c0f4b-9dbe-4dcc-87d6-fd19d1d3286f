{"name": "uclip", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "~2", "@tauri-apps/plugin-fs": "~2", "@tauri-apps/plugin-opener": "^2.2.7", "@xstate/store": "^3.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "color-convert": "^3.1.0", "lucide-react": "^0.513.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-resizable-panels": "^3.0.2", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@tailwindcss/vite": "^4.1.8", "@tauri-apps/cli": "^2.5.0", "@types/node": "^22.15.30", "@types/react": "^19.1.7", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.1", "tailwindcss": "^4.1.8", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.4", "typescript": "~5.6.3", "vite": "^6.3.5"}}